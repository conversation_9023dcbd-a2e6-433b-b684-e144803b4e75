<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico?v=1">
  <title>集团审批</title>
  <style>
    html, body {
      background-color: #fff;
      margin: 0;
      padding: 0;
    }
    .loading-tips {
      width: 500px;
      height: 500px;
      margin: 0 auto;
      background: url('./static/23.webp') no-repeat;
      position: relative;
    }
    .text-tips {
      text-align: center;
      color: #666;
      font-size: 14px;
      position: absolute;
      top: 320px;
      left: 0;
      right: 0;
    }
    .download-btn {
      color: #fff;
      background-color: #409eff;
      border-color: #409eff;
      font-weight: 500;
      text-align: center;
      padding: 12px 20px;
      font-size: 14px;
      border-radius: 4px;
      line-height: 1;
      white-space: nowrap;
      cursor: pointer;
      position: fixed;
      bottom: 15px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 9999999999;
    }
  </style>
</head>
<body>
  <div id="loadingTips" class="loading-tips">
    <div class="text-tips">文档较大，加载可能需要一些时间，请耐心等待</div>
  </div>
  <a id="downloadBtn" class="download-btn" href="" download target="_blank">网页有问题？下载看看吧</a>
  <r-preview id="previewContainer" src="" closeable="false"></r-preview>
  
  <script>
    let search = window.location.search
    let searchArr = search.split('&')

    let curDurl = searchArr.filter(item => item.includes('furl='))[0]
    curDurl = curDurl.split('=')[1]
    let downloadDom = document.getElementById('downloadBtn')
    console.log('curDurl: ', curDurl)
    downloadDom.onclick = function(e) {
      e.preventDefault()
      window.open(decodeURIComponent(curDurl))
    }
    
    window.onload = function() {
      let loadingDom = document.getElementById('loadingTips')
      loadingDom.style.display = 'none'

      let curFurl = searchArr.filter(item => item.includes('furl='))[0]
      curFurl = curFurl ? curFurl.split('=')[1] : ''
      if(curFurl) {
        let previewDom = document.getElementById('previewContainer')
        previewDom.src = decodeURIComponent(curFurl)
        console.log('curFurl: ', decodeURIComponent(curFurl))
      }
    }
  </script>
  <script src="./static/index.umd.cjs"></script>
</body>

</html>