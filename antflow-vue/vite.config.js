import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import createVitePlugins from './vite/plugins'

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV,VITE_HOME_PATH } = env
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上 
    base: VITE_APP_ENV === 'production' ? VITE_HOME_PATH : '/',
    plugins: createVitePlugins(env, command === 'build'),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src')
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    optimizeDeps: {
      include: ['@/./lib/vForm/designer.umd.js']  //此处路径必须跟main.js中import路径完全一致！
    },
    build: {
      /* 其他build生产打包配置省略 */
      //...
      commonjsOptions: {
          include: /node_modules|lib/  //这里记得把lib目录加进来，否则生产打包会报错！！
      },
      rollupOptions: {
        output: {
          manualChunks: {
            // 将 VForm 单独打包
            'vform': ['@/./lib/vForm/designer.umd.js'],
            // 将 Element Plus 组件库单独打包
            'element-plus': ['element-plus'],
            // 将 Vue 相关库单独打包
            'vue': ['vue'],
            'vue-router': ['vue-router'],
            'axios': ['axios']
          }
        }
      },
      // 启用 CSS 代码分割
      cssCodeSplit: true
    },
    // vite 相关配置
    server: {
      port: 80,
      host: true,
      open: true,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        '/dev-api': {
          target: 'http://localhost:8080',
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api/, '')
        }
      }
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              }
            }
          }
        ]
      }
    }
  }
})